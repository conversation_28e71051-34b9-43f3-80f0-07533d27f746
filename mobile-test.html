<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>移动端测试 - 缩短输入框版本</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    #amap-canvas {
      background: linear-gradient(45deg, #e8f5e8, #f0f8f0);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #666;
    }
    
    .test-info {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 1000;
    }
  </style>
</head>
<body>
<div class="a4-container">
  <div style="text-align: center; font-size:25px; font-weight:bold; margin:18px 0 6px;">工厂店铺分布</div>
  <div style="text-align:center;color:#555; font-size:15px;">拖动地图、滚轮缩放后，自动检索当前区域</div>
  <div id="amap-canvas">
    <div class="keyword-search-box">
      <input class="keyword-input" type="text" placeholder="关键词" value="汽修" />
      <span class="range-label">范围</span>
      <input class="range-input" type="number" min="0.5" max="100" step="0.5" value="5" />
      <span class="range-label">公里</span>
      <button class="search-btn">搜索</button>
    </div>
    <button class="refresh-btn">⟳ 刷新附近</button>
    <button class="my-location-btn" title="回到当前位置">📍</button>
    
    <div style="text-align: center;">
      <h3>地图区域 (测试用)</h3>
      <p>Table布局 + 缩短输入框测试</p>
    </div>
  </div>
</div>

<div class="test-info">
  <strong>终极一行布局测试：</strong><br>
  1. 所有元素都使用table-cell布局<br>
  2. 关键词输入框：80px → 70px(移动端)<br>
  3. 数字输入框：40px → 35px(移动端)<br>
  4. 移除了多余的容器，直接一行显示<br>
  5. 桌面端和移动端都应该是一行布局
</div>

<script>
  document.querySelector('.search-btn').addEventListener('click', function() {
    alert('搜索测试成功！');
  });
</script>
</body>
</html>
