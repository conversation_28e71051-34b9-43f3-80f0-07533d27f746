html, body {height: 100%;margin: 0; padding: 0; background: #fff;}
.a4-container {width: 794px;height: 1123px; margin: 20px auto; border: 1px solid #ccc; background: #fff;}
#amap-canvas {width: 100%; height: 1000px; position: relative; overflow: hidden;}

/* 移动端响应式布局 */
@media screen and (max-width: 768px) {
  .a4-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border: none;
  }

  #amap-canvas {
    height: 100vh;
  }

  /* 移动端搜索框优化 */
  .keyword-search-box {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    width: calc(100% - 20px);
    flex-direction: column;
    gap: 6px;
    padding: 8px 12px;
  }

  .keyword-input {
    width: 100%;
    max-width: none;
    font-size: 16px;
    padding: 8px 12px;
  }

  .range-input {
    width: 80px;
    font-size: 16px;
    padding: 8px 10px;
  }

  .search-btn {
    width: 100%;
    padding: 10px 15px;
    font-size: 16px;
    margin-left: 0;
    margin-top: 4px;
  }

  .range-label {
    font-size: 14px;
  }

  /* 移动端按钮优化 */
  .refresh-btn {
    left: 10px;
    top: 120px;
    font-size: 14px;
    padding: 8px 16px;
  }

  .countdown-hint {
    left: 10px;
    top: 170px;
    font-size: 13px;
    max-width: calc(100% - 20px);
  }

  .my-location-btn {
    right: 15px;
    bottom: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  /* 移动端标题隐藏，避免与搜索框重叠 */
  .a4-container > div:first-child,
  .a4-container > div:nth-child(2) {
    display: none;
  }
}
.refresh-btn {position:absolute;left:18px;top:17px;z-index:102;background:#f55;color:#fff;font-size:17px;font-weight:bold;border-radius:5px;border:0;cursor:pointer;padding:6px 20px;box-shadow: 0 2px 8px #b00a;transition: background .15s;max-width: 160px;}
.refresh-btn:hover { background: #d22;}
.countdown-hint {position: absolute; left:190px;top:24px;z-index:103;color: #919; font-size:15px; font-weight:bold;background: #fff4; border-radius: 3px; padding: 2px 10px;pointer-events: none; transition: opacity 0.2s;max-width: 340px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;}
.keyword-search-box {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255,255,255,0.94);
  border-radius: 7px;
  box-shadow: 0 2px 14px rgba(187,187,187,0.5);
  padding: 3px 10px 3px 10px;
  max-width: 96%;
  flex-wrap: wrap;
}
.keyword-input {font-size:16px; border:1px solid #ccc;border-radius:4px; padding:5px 8px; outline:none; min-width:0; max-width:120px;}
.range-input {width:62px; font-size:16px;border:1px solid #ccc; border-radius: 4px; padding:5px 5px 5px 10px;outline:none; text-align:right; min-width:0;}
.range-label {margin-left:3px; color: #666;font-size:15px;}
.search-btn {background: #44a; color: #fff; border: 0;border-radius:4px; font-size:15px;font-weight:bold;padding:6px 15px; cursor:pointer; transition: background .15s;margin-left: 2px;}
.search-btn:hover {background:#206;}
.my-red-dot {width: 16px;height: 16px; background: rgba(255,0,0,0.85);border:2px solid #fff; border-radius: 50%;box-shadow: 0 0 7px #900; cursor: pointer; transition:transform .15s;position: absolute;top: 0; left: 0;will-change: transform;}
.my-red-dot:hover { transform: scale(1.2);}
.info-window {line-height: 1.6; min-width: 180px; max-width: 280px;padding: 8px 12px; background: #fff;border-radius: 7px; border:1.5px solid #b00;box-shadow:0 2px 16px #8002; color:#111; font-size: 14px;}
.amap-marker-label {z-index: 20;}
.my-location-btn {position:absolute; right:28px; bottom:28px; z-index:200;width:46px;height:46px;background:rgba(255,255,255,0.96);color:#1976d2;border-radius:50%;border:2px solid #eee;font-size:29px;cursor:pointer;box-shadow: 0 2px 12px #aaa4;outline:none;transition:transform 0.14s, box-shadow 0.18s;display:flex;align-items:center;justify-content:center;}
.my-location-btn:hover {background: #e0f0ff;box-shadow: 0 2px 18px #6cf6;transform: scale(1.08);}
@media print { .info-window, .refresh-btn, .my-location-btn, .countdown-hint, .keyword-search-box { display:none!important;}}