<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>工厂店铺分布11</title>
  <link rel="stylesheet" href="style.css" />
  <script src="https://webapi.amap.com/maps?v=2.0&key=835a3d283857c293653c3a6a112f8cb2"></script>
</head>
<body>
<div class="a4-container">
  <div style="text-align: center; font-size:25px; font-weight:bold; margin:18px 0 6px;">工厂店铺分布</div>
  <div style="text-align:center;color:#555; font-size:15px;">拖动地图、滚轮缩放后，自动检索当前区域</div>
  <div id="amap-canvas">
    <div class="keyword-search-box">
      <input class="keyword-input" type="text" placeholder="关键词（如4S店、保养）" value="汽修" />
      <div class="range-search-row">
        <span class="range-label">范围</span>
        <input class="range-input" type="number" min="0.5" max="100" step="0.5" value="5" />
        <span class="range-label">公里</span>
        <button class="search-btn">搜索</button>
      </div>
    </div>
    <button class="refresh-btn">⟳ 刷新附近</button>
    <div class="countdown-hint" style="display:none;"></div>
    <button class="my-location-btn" title="回到当前位置">📍</button>
  </div>
</div>
<script src="script.js"></script>
</body>
</html>