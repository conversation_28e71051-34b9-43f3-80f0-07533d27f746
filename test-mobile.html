<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>移动端测试 - 工厂店铺分布</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    /* 测试用的简化地图样式 */
    #amap-canvas {
      background: linear-gradient(45deg, #e8f5e8, #f0f8f0);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #666;
    }
    
    /* 测试提示 */
    .test-info {
      position: fixed;
      bottom: 20px;
      left: 20px;
      right: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 1000;
    }
  </style>
</head>
<body>
<div class="a4-container">
  <div style="text-align: center; font-size:25px; font-weight:bold; margin:18px 0 6px;">工厂店铺分布</div>
  <div style="text-align:center;color:#555; font-size:15px;">拖动地图、滚轮缩放后，自动检索当前区域</div>
  <div id="amap-canvas">
    <div class="keyword-search-box">
      <input class="keyword-input" type="text" placeholder="关键词（如4S店、保养）" value="汽修" />
      <span class="range-label">范围</span>
      <input class="range-input" type="number" min="0.5" max="100" step="0.5" value="5" />
      <span class="range-label">公里</span>
      <button class="search-btn">搜索</button>
    </div>
    <button class="refresh-btn">⟳ 刷新附近</button>
    <div class="countdown-hint" style="display:none;"></div>
    <button class="my-location-btn" title="回到当前位置">📍</button>
    
    <div style="text-align: center;">
      <h3>地图区域 (测试用)</h3>
      <p>这里应该显示高德地图</p>
    </div>
  </div>
</div>

<div class="test-info">
  <strong>测试说明：</strong><br>
  1. 搜索框应该固定在页面顶部中央<br>
  2. 在移动端(宽度≤768px)时，搜索框应该变为垂直布局<br>
  3. 页面应该没有横向滚动条<br>
  4. 使用浏览器开发者工具切换到移动设备视图进行测试
</div>

<script>
  // 简单的测试脚本
  document.querySelector('.search-btn').addEventListener('click', function() {
    alert('搜索功能正常！关键词：' + document.querySelector('.keyword-input').value);
  });
  
  document.querySelector('.refresh-btn').addEventListener('click', function() {
    alert('刷新功能正常！');
  });
  
  document.querySelector('.my-location-btn').addEventListener('click', function() {
    alert('定位功能正常！');
  });
</script>
</body>
</html>
