<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>微信浏览器兼容性测试 - 工厂店铺分布</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    /* 测试用的简化地图样式 */
    #amap-canvas {
      background: linear-gradient(45deg, #e8f5e8, #f0f8f0);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #666;
    }
    
    /* 测试提示 */
    .test-info {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 1000;
      max-height: 120px;
      overflow-y: auto;
    }
    
    .browser-info {
      position: fixed;
      top: 160px;
      left: 20px;
      right: 20px;
      background: rgba(0,100,200,0.8);
      color: white;
      padding: 8px;
      border-radius: 5px;
      font-size: 11px;
      z-index: 1000;
    }
  </style>
</head>
<body>
<div class="a4-container">
  <div style="text-align: center; font-size:25px; font-weight:bold; margin:18px 0 6px;">工厂店铺分布</div>
  <div style="text-align:center;color:#555; font-size:15px;">拖动地图、滚轮缩放后，自动检索当前区域</div>
  <div id="amap-canvas">
    <div class="keyword-search-box">
      <input class="keyword-input" type="text" placeholder="关键词（如4S店、保养）" value="汽修" />
      <div class="range-search-row">
        <span class="range-label">范围</span>
        <input class="range-input" type="number" min="0.5" max="100" step="0.5" value="5" />
        <span class="range-label">公里</span>
        <button class="search-btn">搜索</button>
      </div>
    </div>
    <button class="refresh-btn">⟳ 刷新附近</button>
    <div class="countdown-hint" style="display:none;"></div>
    <button class="my-location-btn" title="回到当前位置">📍</button>
    
    <div style="text-align: center;">
      <h3>地图区域 (测试用)</h3>
      <p>这里应该显示高德地图</p>
    </div>
  </div>
</div>

<div class="test-info">
  <strong>微信浏览器兼容性测试：</strong><br>
  1. 搜索框应该固定在页面底部<br>
  2. 第一行：关键词输入框（独占一行）<br>
  3. 第二行：范围 + 数字 + 公里 + 搜索按钮（同一行）<br>
  4. 在微信中打开应该与普通浏览器显示一致
</div>

<div class="browser-info">
  <strong>浏览器信息：</strong><span id="userAgent"></span>
</div>

<script>
  // 显示浏览器信息
  document.getElementById('userAgent').textContent = navigator.userAgent;
  
  // 简单的测试脚本
  document.querySelector('.search-btn').addEventListener('click', function() {
    const keyword = document.querySelector('.keyword-input').value;
    const range = document.querySelector('.range-input').value;
    alert('搜索功能正常！\n关键词：' + keyword + '\n范围：' + range + '公里');
  });
  
  document.querySelector('.refresh-btn').addEventListener('click', function() {
    alert('刷新功能正常！');
  });
  
  document.querySelector('.my-location-btn').addEventListener('click', function() {
    alert('定位功能正常！');
  });
  
  // 检测是否在微信浏览器中
  function isWechat() {
    return /micromessenger/i.test(navigator.userAgent);
  }
  
  if (isWechat()) {
    console.log('当前在微信浏览器中');
    document.querySelector('.browser-info').style.backgroundColor = 'rgba(0,150,0,0.8)';
    document.querySelector('.browser-info').innerHTML = '<strong>✓ 微信浏览器检测成功</strong>';
  } else {
    console.log('当前在普通浏览器中');
  }
</script>
</body>
</html>
